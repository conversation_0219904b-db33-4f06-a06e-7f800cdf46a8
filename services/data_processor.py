# -*- coding: utf-8 -*-
"""
数据处理服务模块
负责解析和转换爬取的原始数据
"""

import re
from urllib.parse import unquote
from typing import Dict, List, Tuple, Any, Optional

from utils import get_logger
from models import User, UserNew, Group, Topic, TopicImage, Comment, Like, QAndA, CheckinTask
from services.content_cleaner import ContentCleaner

logger = get_logger(__name__)


class DataProcessor:
    """数据处理器"""

    def __init__(self):
        self.content_cleaner = ContentCleaner()

    def clean_and_convert_content(self, text: str) -> str:
        """清理和转换内容"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空白
        text = text.strip()

        return text
    
    def parse_user_from_dict(self, user_data: Dict[str, Any]) -> Optional[User]:
        """从字典解析用户数据（旧版本，兼容性保留）"""
        if not user_data or not user_data.get('user_id'):
            return None

        return User(
            user_id=int(user_data['user_id']),
            name=user_data.get('name'),
            alias=user_data.get('alias'),
            description=user_data.get('description'),
            location=user_data.get('location'),
            avatar_url=user_data.get('avatar_url'),
            join_time=user_data.get('join_time'),
            status=user_data.get('status')
        )

    def parse_user_new_from_dict(self, user_data: Dict[str, Any], mid: Optional[int] = None) -> Optional[UserNew]:
        """
        从字典解析用户数据到新的 UserNew 模型

        Args:
            user_data: 用户数据字典
            mid: 用户的 mid (zsxq_group_member_id)，如果提供则使用，否则尝试从 user_data 中获取

        Returns:
            UserNew: 解析后的用户对象，如果解析失败返回 None
        """
        if not user_data:
            return None

        # 获取 mid
        if mid is None:
            mid = user_data.get('mid') or user_data.get('number') or user_data.get('zsxq_group_member_id')

        if mid is None:
            logger.warning(f"无法获取用户的 mid: {user_data}")
            return None

        # 获取 user_id (uid)
        user_id = user_data.get('user_id')
        if user_id:
            user_id = int(user_id)

        return UserNew(
            zsxq_group_member_id=int(mid),
            user_id=user_id,
            name=user_data.get('name'),
            alias=user_data.get('alias'),
            avatar_url=user_data.get('avatar_url'),
            description=user_data.get('description') or user_data.get('introduction'),
            join_time=user_data.get('join_time'),
            expiration_time=user_data.get('expiration_time'),
            status=user_data.get('status'),
            location=user_data.get('location')
        )
    
    def parse_group_from_dict(self, group_data: Dict[str, Any]) -> Optional[Group]:
        """从字典解析星球数据"""
        if not group_data or not group_data.get('group_id'):
            return None
        
        return Group(
            group_id=int(group_data['group_id']),
            name=group_data.get('name'),
            type=group_data.get('type'),
            background_url=group_data.get('background_url')
        )
    
    def extract_owner_info(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """从不同类型的 topic 结构中安全地提取 owner 信息"""
        topic_type = topic.get("type")

        if not topic_type:
            return {}

        # 根据不同的 topic 类型提取 owner 信息
        if topic_type == "q&a":
            # 问答类型：owner 在 question.owner 中
            return topic.get("question", {}).get("owner", {})
        elif topic_type == "solution":
            # 解决方案类型：owner 在 solution.owner 中
            return topic.get("solution", {}).get("owner", {})
        elif topic_type == "talk":
            # 讨论类型：owner 在 talk.owner 中
            return topic.get("talk", {}).get("owner", {})
        else:
            # 其他类型：尝试通用方式
            return topic.get(topic_type, {}).get("owner", {})
    
    def parse_topic_content(self, raw_text: str) -> Tuple[Optional[str], List[str], str]:
        """
        解析帖子原文，提取标题、标签，并返回清理后的正文

        Returns:
            Tuple[title, tags, cleaned_text]
        """
        if not raw_text:
            return None, [], ""

        # 使用新的内容清洗器
        cleaned_content = self.content_cleaner.clean_topic_content(raw_text)

        return (
            cleaned_content['title'],
            cleaned_content['tags'],
            cleaned_content['content']
        )
    
    def parse_topic_images(self, topic: Dict[str, Any]) -> List[TopicImage]:
        """解析帖子图片"""
        topic_id = topic.get('topic_id')
        topic_type = topic.get('type')
        
        if not all([topic_id, topic_type]):
            return []
        
        images = topic.get(topic_type, {}).get('images', [])
        topic_images = []
        
        for image in images:
            image_id = image.get('image_id')
            original_image = image.get('original', {})
            image_url = original_image.get('url')
            
            if not all([image_id, image_url]):
                continue
            
            topic_images.append(TopicImage(
                image_id=image_id,
                topic_id=topic_id,
                url=image_url,
                type=image.get('type'),
                width=original_image.get('width'),
                height=original_image.get('height'),
                size_bytes=original_image.get('size')
            ))
        
        return topic_images

    def _resolve_owner_id(self, owner_info: Dict[str, Any], user_cache_service=None) -> Optional[int]:
        """
        解析 owner_id：将 user_id 映射到 users 表的 id

        Args:
            owner_info: owner 信息字典
            user_cache_service: 用户缓存服务实例

        Returns:
            int: users 表的 id，如果无法解析返回 None
        """
        if not owner_info or not user_cache_service:
            return None

        uid = owner_info.get('user_id')
        if not uid:
            return None

        try:
            uid = int(uid)
            # 使用用户缓存服务获取或更新用户ID
            return user_cache_service.get_or_update_user_id(uid)
        except (ValueError, TypeError):
            logger.warning(f"无效的 user_id: {uid}")
            return None
    
    def parse_topic_from_dict(self, topic_data: Dict[str, Any], user_cache_service=None) -> Optional[Topic]:
        """从字典解析帖子数据"""
        if not topic_data or not topic_data.get('topic_id'):
            return None

        topic_id = int(topic_data['topic_id'])
        topic_type = topic_data.get('type')

        # 检查是否为打卡任务类型
        if topic_data.get('checkin'):
            # 如果存在 checkin 字段，则类型为 'checkin'
            topic_type = 'checkin'
            logger.debug(f"Topic {topic_id}: 识别为打卡任务类型")

        # 优先使用HTML文本，如果没有则使用普通文本
        html_text = topic_data.get(topic_type, {}).get('html_text', '') if topic_type else ''
        raw_text = topic_data.get(topic_type, {}).get('text', '') if topic_type else ''

        # 选择要存储的文本内容：优先使用HTML格式
        text_content = html_text if html_text else raw_text

        # 调试日志
        if html_text:
            logger.debug(f"Topic {topic_id}: 使用HTML内容，长度 {len(html_text)}")
        else:
            logger.debug(f"Topic {topic_id}: 使用普通文本，长度 {len(raw_text)}")

        # 优先使用已清洗的标签和标题数据，如果没有则重新解析
        if 'tags' in topic_data and 'title' in topic_data:
            # 使用已清洗的数据
            title = topic_data.get('title')
            tags = topic_data.get('tags', [])
            logger.debug(f"Topic {topic_id}: 使用已清洗的标签数据: {tags}")
        else:
            # 重新解析内容（用于提取标题和标签）
            title, tags, cleaned_text = self.parse_topic_content(raw_text)
            logger.debug(f"Topic {topic_id}: 重新解析得到标签: {tags}")

        # 解析图片
        images = self.parse_topic_images(topic_data)

        # 提取 owner 信息并解析 owner_id
        owner_info = self.extract_owner_info(topic_data)
        owner_id = self._resolve_owner_id(owner_info, user_cache_service)
        
        return Topic(
            topic_id=topic_id,
            group_id=topic_data.get('group', {}).get('group_id'),
            type=topic_type,
            owner_id=owner_id,  # 使用解析后的 owner_id (users.id)
            title=title or topic_data.get('title'),
            text=text_content,  # 优先使用HTML格式的内容
            raw_text=raw_text,
            likes_count=topic_data.get('likes_count'),
            comments_count=topic_data.get('comments_count'),
            reading_count=topic_data.get('reading_count'),
            create_time=topic_data.get('create_time'),
            modify_time=topic_data.get('modify_time'),
            digested=topic_data.get('digested'),
            sticky=topic_data.get('sticky'),
            tags=tags,
            images=images
        )
    
    def parse_comments_from_topic(self, topic_data: Dict[str, Any], user_cache_service=None) -> List[Comment]:
        """从帖子数据中解析评论"""
        topic_id = topic_data.get('topic_id')
        if not topic_id:
            return []

        comments = topic_data.get("show_comments", [])
        comment_list = []

        for comment in comments:
            owner = comment.get("owner")
            if owner and comment.get("comment_id"):
                # 解析owner_id：将user_id映射到users表的id
                owner_id = self._resolve_owner_id(owner, user_cache_service)

                comment_list.append(Comment(
                    comment_id=comment['comment_id'],
                    topic_id=topic_id,
                    owner_id=owner_id,  # 使用解析后的owner_id
                    text=comment.get('text'),
                    likes_count=comment.get('likes_count'),
                    rewards_count=comment.get('rewards_count'),
                    sticky=comment.get('sticky'),
                    create_time=comment.get('create_time')
                ))

        return comment_list
    
    def parse_likes_from_topic(self, topic_data: Dict[str, Any], user_cache_service=None) -> List[Like]:
        """从帖子数据中解析点赞"""
        topic_id = topic_data.get('topic_id')
        if not topic_id:
            return []

        likes = topic_data.get("latest_likes", [])
        like_list = []

        for like in likes:
            owner = like.get("owner")
            if owner and owner.get("user_id"):
                # 解析user_id：将user_id映射到users表的id
                resolved_user_id = self._resolve_owner_id(owner, user_cache_service)

                like_list.append(Like(
                    topic_id=topic_id,
                    user_id=resolved_user_id or owner['user_id'],  # 优先使用解析后的ID，失败则使用原始ID
                    create_time=like.get('create_time'),
                    emoji_key='[赞]'
                ))

        return like_list
    
    def parse_qna_from_topic(self, topic_data: Dict[str, Any]) -> Optional[QAndA]:
        """从帖子数据中解析问答"""
        if topic_data.get('type') != 'q&a':
            return None

        topic_id = topic_data.get('topic_id')
        if not topic_id:
            return None

        return QAndA(
            topic_id=topic_id,
            question_text=topic_data.get('question', {}).get('text'),
            questionee_id=topic_data.get('question', {}).get('questionee', {}).get('user_id'),
            answer_text=topic_data.get('answer', {}).get('text'),
            answered=topic_data.get('answered')
        )

    def parse_checkin_from_topic(self, topic_data: Dict[str, Any]) -> Optional[CheckinTask]:
        """从帖子数据中解析打卡任务"""
        # 检查是否存在 checkin 字段
        if not topic_data.get('checkin'):
            return None

        topic_id = topic_data.get('topic_id')
        if not topic_id:
            return None

        checkin_data = topic_data.get('checkin', {})

        return CheckinTask(
            topic_id=topic_id,
            checkin_id=checkin_data.get('checkin_id'),
            title=checkin_data.get('title'),
            min_words_count=checkin_data.get('min_words_count', 0)
        )
    
    def extract_all_users_from_topic(self, topic_data: Dict[str, Any]) -> List[User]:
        """从帖子数据中提取所有用户信息"""
        users = []
        
        # 帖子作者
        owner_info = self.extract_owner_info(topic_data)
        if owner_info:
            user = self.parse_user_from_dict(owner_info)
            if user:
                users.append(user)
        
        # 问答中的用户
        questionee = topic_data.get('question', {}).get('questionee')
        if questionee:
            user = self.parse_user_from_dict(questionee)
            if user:
                users.append(user)
        
        answer_owner = topic_data.get('answer', {}).get('owner')
        if answer_owner:
            user = self.parse_user_from_dict(answer_owner)
            if user:
                users.append(user)
        
        # 评论中的用户
        for comment in topic_data.get("show_comments", []):
            if comment.get("owner"):
                user = self.parse_user_from_dict(comment["owner"])
                if user:
                    users.append(user)
        
        # 点赞中的用户
        for like in topic_data.get("latest_likes", []):
            if like.get("owner"):
                user = self.parse_user_from_dict(like["owner"])
                if user:
                    users.append(user)
        
        return users
