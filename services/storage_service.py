# -*- coding: utf-8 -*-
"""
存储服务模块
负责文件上传和数据库操作
"""

import time
import requests
from typing import Optional, List
from pathlib import Path

from supabase import create_client, Client

from config import config
from utils import get_logger
from models import User, UserNew, Group, Topic, TopicImage, Comment, Like, QAndA, CheckinTask

logger = get_logger(__name__)


class StorageService:
    """存储服务"""
    
    def __init__(self):
        self.config = config.supabase
        self.client: Client = create_client(self.config.url, self.config.key)
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        }
        # 延迟导入避免循环依赖
        self._image_processor = None
    
    def upload_image(self, image_url: str, storage_path_base: str, bucket_name: str) -> Optional[str]:
        """
        从 URL 下载图片并上传到 Supabase Storage
        
        Args:
            image_url: 图片 URL
            storage_path_base: 存储路径基础名（不含扩展名）
            bucket_name: 存储桶名称
            
        Returns:
            str: 存储路径，失败返回 None
        """
        if not image_url:
            return None
        
        max_retries = config.zsxq.max_retries
        retry_delay = config.zsxq.retry_wait_time
        
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    image_url, 
                    headers=self.headers, 
                    timeout=config.zsxq.request_timeout, 
                    stream=True
                )
                response.raise_for_status()
                
                content_type = response.headers.get('content-type', 'application/octet-stream')
                ext_map = {
                    'image/jpeg': '.jpg', 
                    'image/png': '.png', 
                    'image/gif': '.gif', 
                    'image/webp': '.webp'
                }
                ext = ext_map.get(content_type, '.jpg')
                final_storage_path = f"{storage_path_base}{ext}"
                
                self.client.storage.from_(bucket_name).upload(
                    path=final_storage_path, 
                    file=response.content,
                    file_options={"content-type": content_type, "upsert": "true"}
                )
                
                time.sleep(0.5)  # 避免请求过快
                logger.debug(f"成功上传图片: {final_storage_path}")
                return final_storage_path
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"下载失败 (第 {attempt + 1}/{max_retries} 次): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    logger.error(f"达到最大重试次数，放弃下载 {image_url}")
            except Exception as e:
                logger.error(f"图片上传至 Supabase 失败: {e}")
                break
        
        return None

    @property
    def image_processor(self):
        """延迟加载图片处理器"""
        if self._image_processor is None:
            from services.image_processor import ImageProcessor
            self._image_processor = ImageProcessor()
        return self._image_processor

    def _upload_image_data(self, image_data: bytes, storage_path_base: str, bucket_name: str) -> Optional[str]:
        """
        直接上传图片数据到 Supabase Storage

        Args:
            image_data: 图片数据
            storage_path_base: 存储路径基础名（不含扩展名）
            bucket_name: 存储桶名称

        Returns:
            str: 存储路径，失败返回 None
        """
        try:
            # 获取图片信息以确定格式
            image_info = self.image_processor.get_image_info(image_data)
            if not image_info:
                logger.error("无法获取图片信息")
                return None

            # 根据格式确定扩展名
            format_to_ext = {
                'JPEG': '.jpg',
                'PNG': '.png',
                'GIF': '.gif',
                'WEBP': '.webp'
            }
            ext = format_to_ext.get(image_info.get('format', ''), '.jpg')
            final_storage_path = f"{storage_path_base}{ext}"

            # 确定 content-type
            ext_to_content_type = {
                '.jpg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp'
            }
            content_type = ext_to_content_type.get(ext, 'image/jpeg')

            # 上传到 Supabase Storage
            self.client.storage.from_(bucket_name).upload(
                path=final_storage_path,
                file=image_data,
                file_options={"content-type": content_type, "upsert": "true"}
            )

            logger.debug(f"成功上传图片数据: {final_storage_path}")
            return final_storage_path

        except Exception as e:
            logger.error(f"上传图片数据失败: {e}")
            return None
    
    def save_user(self, user: User) -> bool:
        """保存用户数据（旧版本，兼容性保留）"""
        try:
            # 上传头像
            if user.avatar_url:
                user.avatar_path = self.upload_image(
                    user.avatar_url,
                    str(user.user_id),
                    self.config.avatars_bucket
                )

            user_record = {
                'user_id': user.user_id,
                'name': user.name,
                'alias': user.alias,
                'description': user.description,
                'location': user.location,
                'avatar_path': user.avatar_path,
                'join_time': user.join_time,
                'status': user.status
            }

            self.client.table('users').upsert(user_record).execute()
            logger.debug(f"成功保存用户: {user.user_id}")
            return True

        except Exception as e:
            logger.error(f"保存用户 {user.user_id} 失败: {e}")
            return False

    def save_user_new(self, user: UserNew) -> bool:
        """保存用户数据到 users 表"""
        try:
            # 先检查用户是否已存在（通过MID）
            existing_user = self.client.table(
                'users').select('id, user_id').eq('zsxq_group_member_id', user.zsxq_group_member_id).execute()

            # 上传头像
            if user.avatar_url and user.user_id:
                # 使用 uid 作为文件名标识
                user.avatar_path = self.upload_image(
                    user.avatar_url,
                    str(user.user_id),
                    self.config.avatars_bucket
                )

            user_record = {
                'zsxq_group_member_id': user.zsxq_group_member_id,
                'user_id': user.user_id,
                'name': user.name,
                'alias': user.alias,
                'avatar_path': user.avatar_path,
                'description': user.description,
                'join_time': user.join_time,
                'expiration_time': user.expiration_time,
                'phone_number': user.phone_number,
                'wechat_id': user.wechat_id,
                'profile_data': user.profile_data
            }

            # 移除 None 值
            user_record = {k: v for k, v in user_record.items() if v is not None}

            user_db_id = None

            if existing_user.data:
                # 用户已存在，更新记录
                existing_id = existing_user.data[0]['id']
                existing_uid = existing_user.data[0].get('user_id')
                user_db_id = existing_id

                # 如果现有记录没有 user_id，或者新的 user_id 不同，则更新
                if not existing_uid or (user.user_id and existing_uid != user.user_id):
                    self.client.table('users').update(user_record).eq('id', existing_id).execute()
                    logger.debug(f"更新现有用户: mid={user.zsxq_group_member_id}, uid={user.user_id}")

                    # 更新缓存
                    self._update_user_cache_after_save(user_db_id, user)
                else:
                    logger.debug(f"用户已存在且信息一致: mid={user.zsxq_group_member_id}, uid={existing_uid}")
            else:
                # 用户不存在，插入新记录
                response = self.client.table('users').insert(user_record).execute()
                if response.data and len(response.data) > 0:
                    user_db_id = response.data[0]['id']
                    logger.debug(f"插入新用户: mid={user.zsxq_group_member_id}, uid={user.user_id}, db_id={user_db_id}")

                    # 更新缓存
                    self._update_user_cache_after_save(user_db_id, user)

            return True

        except Exception as e:
            logger.error(f"保存用户到 users 失败: mid={user.zsxq_group_member_id}, uid={user.user_id}, 错误: {e}")
            return False

    def _update_user_cache_after_save(self, user_db_id: int, user: UserNew):
        """保存用户后更新缓存"""
        try:
            from services.user_cache_service import UserCacheService
            cache_service = UserCacheService()
            cache_service._update_cache_with_new_user(user_db_id, user)
            logger.debug(f"已更新用户缓存: db_id={user_db_id}, uid={user.user_id}, mid={user.zsxq_group_member_id}")
        except Exception as e:
            logger.warning(f"更新用户缓存失败: {e}")
            # 缓存更新失败不影响主流程
    
    def save_group(self, group: Group) -> bool:
        """保存星球数据"""
        try:
            # 上传背景图
            if group.background_url:
                group.background_path = self.upload_image(
                    group.background_url,
                    str(group.group_id),
                    self.config.group_backgrounds_bucket
                )
            
            group_record = {
                'group_id': group.group_id,
                'name': group.name,
                'type': group.type,
                'background_path': group.background_path
            }
            
            self.client.table('groups').upsert(group_record).execute()
            logger.debug(f"成功保存星球: {group.group_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存星球 {group.group_id} 失败: {e}")
            return False
    
    def save_topic(self, topic: Topic) -> bool:
        """保存帖子数据"""
        try:
            # topic.owner_id 已经在 parse_topic_from_dict 中转换为数据库内部ID，直接使用
            topic_record = {
                'topic_id': topic.topic_id,
                'group_id': topic.group_id,
                'type': topic.type,
                'owner_id': topic.owner_id,  # 已经是数据库内部ID
                'title': topic.title,
                'text': topic.text,
                'likes_count': topic.likes_count,
                'comments_count': topic.comments_count,
                'create_time': topic.create_time,
                'modify_time': topic.modify_time,
                'digested': topic.digested,
                'sticky': topic.sticky
            }

            self.client.table('topics').upsert(topic_record).execute()
            logger.debug(f"成功保存帖子: {topic.topic_id}")
            return True

        except Exception as e:
            logger.error(f"保存帖子 {topic.topic_id} 失败: {e}")
            return False
    
    def save_topic_images(self, images: List[TopicImage]) -> bool:
        """保存帖子图片（包含模糊预览）"""
        if not images:
            return True

        try:
            images_to_upsert = []

            for image in images:
                # 处理图片：下载并生成模糊预览
                image_data, blur_data_url = self.image_processor.process_image_with_blur(image.url)

                if image_data:
                    # 上传原始图片到存储
                    storage_path = self._upload_image_data(
                        image_data,
                        f"{image.topic_id}/{image.image_id}",
                        self.config.topic_images_bucket
                    )

                    if storage_path:
                        image.path = storage_path
                        image.blur_data_url = blur_data_url

                        images_to_upsert.append({
                            'image_id': image.image_id,
                            'topic_id': image.topic_id,
                            'path': image.path,
                            'type': image.type,
                            'width': image.width,
                            'height': image.height,
                            'size_bytes': image.size_bytes,
                            'blur_data_url': image.blur_data_url
                        })

                        logger.debug(f"成功处理图片 {image.image_id}，模糊预览: {'有' if blur_data_url else '无'}")
                else:
                    logger.warning(f"无法处理图片 {image.image_id}: {image.url}")

            if images_to_upsert:
                self.client.table('topic_images').upsert(images_to_upsert).execute()
                logger.debug(f"成功保存 {len(images_to_upsert)} 张图片")

            return True

        except Exception as e:
            logger.error(f"保存帖子图片失败: {e}")
            return False
    
    def save_topic_tags(self, topic_id: int, tags: List[str]) -> bool:
        """保存帖子标签"""
        if not tags:
            return True

        try:
            tag_id_map = {}

            # 逐个处理标签，避免批量操作的竞态条件
            for tag_name in tags:
                try:
                    # 先尝试查询是否已存在
                    existing_tag = self.client.table('tags').select('tag_id, name').eq('name', tag_name).execute()

                    if existing_tag.data:
                        # 标签已存在，使用现有ID
                        tag_id_map[tag_name] = existing_tag.data[0]['tag_id']
                    else:
                        # 标签不存在，插入新标签
                        new_tag = self.client.table('tags').insert({'name': tag_name}).execute()
                        if new_tag.data:
                            tag_id_map[tag_name] = new_tag.data[0]['tag_id']

                except Exception as tag_error:
                    # 如果插入失败（可能是并发插入导致的重复），再次查询
                    if 'duplicate key value violates unique constraint' in str(tag_error):
                        existing_tag = self.client.table('tags').select('tag_id, name').eq('name', tag_name).execute()
                        if existing_tag.data:
                            tag_id_map[tag_name] = existing_tag.data[0]['tag_id']
                        else:
                            logger.warning(f"无法获取标签 '{tag_name}' 的ID")
                    else:
                        logger.warning(f"处理标签 '{tag_name}' 时出错: {tag_error}")

            # 插入帖子-标签关联（去重）
            unique_tag_ids = set()
            topic_tag_links = []

            for tag_name in tags:
                tag_id = tag_id_map.get(tag_name)
                if tag_id and tag_id not in unique_tag_ids:
                    topic_tag_links.append({'topic_id': topic_id, 'tag_id': tag_id})
                    unique_tag_ids.add(tag_id)

            if topic_tag_links:
                self.client.table('topic_tags').upsert(topic_tag_links).execute()
                logger.debug(f"成功保存帖子 {topic_id} 的 {len(topic_tag_links)} 个标签")

            return True

        except Exception as e:
            logger.error(f"保存帖子 {topic_id} 标签失败: {e}")
            return False
    
    def save_comments(self, comments: List[Comment]) -> bool:
        """保存评论数据"""
        if not comments:
            return True

        try:
            # 导入用户缓存服务
            from services.user_cache_service import UserCacheService
            user_cache = UserCacheService()

            valid_comment_records = []
            skipped_count = 0

            for comment in comments:
                # 检查评论作者是否存在于用户表中
                if comment.owner_id:
                    user_id = user_cache.get_or_update_user_id(comment.owner_id)
                    if user_id:
                        # 使用数据库中的内部ID
                        valid_comment_records.append({
                            'comment_id': comment.comment_id,
                            'topic_id': comment.topic_id,
                            'owner_id': user_id,
                            'text': comment.text,
                            'likes_count': comment.likes_count,
                            'rewards_count': comment.rewards_count,
                            'sticky': comment.sticky,
                            'create_time': comment.create_time
                        })
                    else:
                        logger.warning(f"跳过评论 {comment.comment_id}：无法找到或创建用户 {comment.owner_id}")
                        skipped_count += 1
                else:
                    logger.warning(f"跳过评论 {comment.comment_id}：缺少 owner_id")
                    skipped_count += 1

            if valid_comment_records:
                self.client.table('topic_comments').upsert(valid_comment_records).execute()
                logger.debug(f"成功保存 {len(valid_comment_records)} 条评论")

            if skipped_count > 0:
                logger.warning(f"跳过了 {skipped_count} 条评论（用户不存在）")

            return True

        except Exception as e:
            logger.error(f"保存评论失败: {e}")
            return False
    
    def save_likes(self, likes: List[Like]) -> bool:
        """保存点赞数据"""
        if not likes:
            return True

        try:
            # 导入用户缓存服务
            from services.user_cache_service import UserCacheService
            user_cache = UserCacheService()

            valid_like_records = []
            skipped_count = 0

            for like in likes:
                # 检查点赞用户是否存在于用户表中
                if like.user_id:
                    user_id = user_cache.get_or_update_user_id(like.user_id)
                    if user_id:
                        # 使用数据库中的内部ID
                        valid_like_records.append({
                            'topic_id': like.topic_id,
                            'user_id': user_id,
                            'create_time': like.create_time,
                            'emoji_key': like.emoji_key
                        })
                    else:
                        logger.warning(f"跳过点赞：无法找到或创建用户 {like.user_id}")
                        skipped_count += 1
                else:
                    logger.warning(f"跳过点赞：缺少 user_id")
                    skipped_count += 1

            if valid_like_records:
                # Supabase 会自动处理唯一约束冲突
                self.client.table('topic_likes').upsert(valid_like_records).execute()
                logger.debug(f"成功保存 {len(valid_like_records)} 条点赞")

            if skipped_count > 0:
                logger.warning(f"跳过了 {skipped_count} 条点赞（用户不存在）")

            return True

        except Exception as e:
            logger.error(f"保存点赞失败: {e}")
            return False
    
    def save_qna(self, qna: QAndA) -> bool:
        """保存问答数据"""
        try:
            # 导入用户缓存服务
            from services.user_cache_service import UserCacheService
            user_cache = UserCacheService()

            # 转换被提问者的UID为数据库内部ID
            questionee_id = None
            if qna.questionee_id:
                questionee_id = user_cache.get_or_update_user_id(qna.questionee_id)
                if not questionee_id:
                    logger.warning(f"问答 {qna.topic_id} 的被提问者 {qna.questionee_id} 不存在，设置为NULL")

            qna_record = {
                'topic_id': qna.topic_id,
                'question_text': qna.question_text,
                'questionee_id': questionee_id,
                'answer_text': qna.answer_text,
                'answered': qna.answered
            }

            self.client.table('q_and_a').upsert(qna_record).execute()
            logger.debug(f"成功保存问答: {qna.topic_id}")
            return True

        except Exception as e:
            logger.error(f"保存问答 {qna.topic_id} 失败: {e}")
            return False

    def save_checkin_task(self, checkin_task: CheckinTask) -> bool:
        """保存打卡任务数据"""
        if not checkin_task:
            return True

        try:
            checkin_record = {
                'topic_id': checkin_task.topic_id,
                'checkin_id': checkin_task.checkin_id,
                'title': checkin_task.title,
                'min_words_count': checkin_task.min_words_count
            }

            self.client.table('checkin_tasks').upsert(checkin_record).execute()
            logger.debug(f"成功保存打卡任务: {checkin_task.topic_id}")
            return True

        except Exception as e:
            logger.error(f"保存打卡任务 {checkin_task.topic_id} 失败: {e}")
            return False
