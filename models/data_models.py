# -*- coding: utf-8 -*-
"""
数据模型定义
定义所有数据结构和类型
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime


@dataclass
class User:
    """用户数据模型（旧版本，兼容性保留）"""
    user_id: int
    name: Optional[str] = None
    alias: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    avatar_url: Optional[str] = None
    avatar_path: Optional[str] = None
    join_time: Optional[str] = None
    status: Optional[str] = None


@dataclass
class UserNew:
    """新用户数据模型（对应 users_new 表）"""
    # 业务身份核心
    zsxq_group_member_id: int  # mid，必需字段
    user_id: Optional[int] = None  # uid，可选字段

    # 基础资料
    name: Optional[str] = None
    alias: Optional[str] = None
    avatar_url: Optional[str] = None  # 用于下载头像
    avatar_path: Optional[str] = None  # 存储路径
    description: Optional[str] = None
    join_time: Optional[str] = None
    expiration_time: Optional[str] = None

    # 个性化服务信息
    phone_number: Optional[str] = None
    wechat_id: Optional[str] = None

    # 扩展字段
    profile_data: Optional[Dict[str, Any]] = None

    # 临时字段（用于处理过程中的状态）
    status: Optional[str] = None
    location: Optional[str] = None


@dataclass
class Group:
    """星球数据模型"""
    group_id: int
    name: Optional[str] = None
    type: Optional[str] = None
    background_url: Optional[str] = None
    background_path: Optional[str] = None


@dataclass
class TopicImage:
    """帖子图片数据模型"""
    image_id: str
    topic_id: int
    url: str
    path: Optional[str] = None
    type: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    size_bytes: Optional[int] = None
    blur_data_url: Optional[str] = None  # 图片模糊预览的DataURL


@dataclass
class Topic:
    """帖子数据模型"""
    topic_id: int
    group_id: Optional[int] = None
    type: Optional[str] = None
    owner_id: Optional[int] = None
    title: Optional[str] = None
    text: Optional[str] = None
    raw_text: Optional[str] = None
    likes_count: Optional[int] = None
    comments_count: Optional[int] = None
    reading_count: Optional[int] = None
    create_time: Optional[str] = None
    modify_time: Optional[str] = None
    digested: Optional[bool] = None
    sticky: Optional[bool] = None
    tags: List[str] = None
    images: List[TopicImage] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.images is None:
            self.images = []


@dataclass
class Comment:
    """评论数据模型"""
    comment_id: int
    topic_id: int
    owner_id: int
    text: Optional[str] = None
    likes_count: Optional[int] = None
    rewards_count: Optional[int] = None
    sticky: Optional[bool] = None
    create_time: Optional[str] = None


@dataclass
class Like:
    """点赞数据模型"""
    topic_id: int
    user_id: int
    create_time: Optional[str] = None
    emoji_key: str = '[赞]'


@dataclass
class QAndA:
    """问答数据模型"""
    topic_id: int
    question_text: Optional[str] = None
    questionee_id: Optional[int] = None
    answer_text: Optional[str] = None
    answered: Optional[bool] = None


@dataclass
class CheckinTask:
    """打卡任务数据模型"""
    topic_id: int
    checkin_id: int
    title: Optional[str] = None
    min_words_count: Optional[int] = None


@dataclass
class ProcessState:
    """处理状态数据模型"""
    processed_user_ids: List[str]
    processed_topic_ids: List[str]
    last_update: Optional[str] = None
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now().isoformat()


@dataclass
class CrawlResult:
    """爬取结果数据模型"""
    success: bool
    total_count: int
    error_message: Optional[str] = None
    file_path: Optional[str] = None
