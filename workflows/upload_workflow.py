# -*- coding: utf-8 -*-
"""
上传工作流
负责协调数据处理和上传流程
"""

import json
from pathlib import Path
from typing import Dict, List, Optional
from collections import defaultdict

from tqdm import tqdm

from config import config
from utils import get_logger
from services import DataProcessor, StorageService, StateManager, UserCacheService
from models import User, Group, Topic
from workflows.user_workflow import UserWorkflow

logger = get_logger(__name__)


class UploadWorkflow:
    """上传工作流"""
    
    def __init__(self):
        self.processor = DataProcessor()
        self.storage = StorageService()
        self.state_manager = StateManager()
        self.user_workflow = UserWorkflow()
        self.user_cache_service = UserCacheService()
        self.data_dir = Path(config.app.data_dir)
    
    def preload_data_from_file(self, file_path: str) -> tuple[Dict[str, User], List[Dict], Optional[Group]]:
        """
        阶段 1: 预扫描文件，收集所有用户和帖子原始数据
        
        Returns:
            tuple[all_users, topics_data, group_info]
        """
        logger.info("--- 阶段 1: 预扫描文件，收集所有用户和星球信息 ---")
        
        all_users = {}
        topics_data = []
        group_info = None
        
        file_path = Path(file_path)
        if not file_path.exists():
            if not file_path.is_absolute():
                file_path = self.data_dir / file_path
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in tqdm(f, desc="预扫描中"):
                try:
                    topic_data = json.loads(line)
                    topics_data.append(topic_data)
                    
                    # 提取星球信息（只需要一次）
                    if not group_info and topic_data.get("group"):
                        group_info = self.processor.parse_group_from_dict(topic_data["group"])
                    
                    # 提取所有用户信息
                    users_in_topic = self.processor.extract_all_users_from_topic(topic_data)
                    for user in users_in_topic:
                        if user and user.user_id:
                            all_users[str(user.user_id)] = user
                            
                except json.JSONDecodeError:
                    continue
        
        logger.info(f"预扫描完成: {len(all_users)} 个唯一用户, {len(topics_data)} 条帖子")
        return all_users, topics_data, group_info
    
    def process_users(self, all_users: Dict[str, User]) -> int:
        """
        阶段 2: 集中处理所有收集到的用户（旧版本，兼容性保留）

        Returns:
            int: 成功处理的用户数量
        """
        logger.info(f"--- 阶段 2: 集中处理 {len(all_users)} 个唯一的用户信息（旧版本） ---")

        success_count = 0

        for user in tqdm(all_users.values(), desc="处理用户"):
            user_id = str(user.user_id)

            # 检查是否已处理
            if self.state_manager.is_user_processed(user_id):
                continue

            # 保存用户
            if self.storage.save_user(user):
                self.state_manager.mark_user_processed(user_id)
                success_count += 1

                # 定期保存进度
                if success_count % 10 == 0:
                    self.state_manager.save_progress()

        # 最终保存进度
        self.state_manager.save_progress()
        logger.info(f"用户处理完成: {success_count} 个用户")
        return success_count

    def process_users_new(self, topics_data: List[Dict]) -> int:
        """
        阶段 2: 使用新的用户处理工作流处理用户

        Args:
            topics_data: 帖子数据列表

        Returns:
            int: 成功处理的用户数量
        """
        logger.info("--- 阶段 2: 使用新的用户处理工作流处理用户 ---")

        results = self.user_workflow.process_users_from_topics(topics_data)

        logger.info(f"用户处理完成: {results}")
        return results['success_count']
    
    def process_group(self, group: Optional[Group]) -> bool:
        """
        阶段 3: 处理星球信息
        
        Returns:
            bool: 是否成功
        """
        if not group:
            logger.warning("没有星球信息需要处理")
            return False
        
        logger.info(f"--- 阶段 3: 处理星球信息: {group.name} ---")
        
        success = self.storage.save_group(group)
        if success:
            logger.info(f"星球信息处理完成: {group.name}")
        else:
            logger.error(f"星球信息处理失败: {group.name}")
        
        return success
    
    def process_topics(self, topics_data: List[Dict], skip_images: bool = False) -> int:
        """
        阶段 4: 插入所有帖子及其完整的关联数据
        
        Args:
            topics_data: 帖子数据列表
            skip_images: 是否跳过图片处理和上传
        
        Returns:
            int: 成功处理的帖子数量
        """
        logger.info(f"--- 阶段 4: 插入 {len(topics_data)} 条帖子及其完整的关联数据 ---")
        if skip_images:
            logger.info("跳过图片处理和上传")
        
        success_count = 0
        
        for topic_data in tqdm(topics_data, desc="处理帖子"):
            topic_id = topic_data.get('topic_id')
            if not topic_id:
                continue
            
            topic_id_str = str(topic_id)
            
            # 检查是否已处理
            if self.state_manager.is_topic_processed(topic_id_str):
                continue
            
            try:
                # 解析帖子数据（传入用户缓存服务）
                topic = self.processor.parse_topic_from_dict(topic_data, self.user_cache_service)
                if not topic:
                    continue
                
                # 保存帖子主体
                if not self.storage.save_topic(topic):
                    continue
                
                # 保存帖子图片（可选跳过）
                if not skip_images and topic.images:
                    self.storage.save_topic_images(topic.images)
                
                # 保存帖子标签
                if topic.tags:
                    self.storage.save_topic_tags(topic.topic_id, topic.tags)
                
                # 保存评论
                comments = self.processor.parse_comments_from_topic(topic_data, self.user_cache_service)
                if comments:
                    self.storage.save_comments(comments)

                # 保存点赞
                likes = self.processor.parse_likes_from_topic(topic_data, self.user_cache_service)
                if likes:
                    self.storage.save_likes(likes)
                
                # 保存问答
                qna = self.processor.parse_qna_from_topic(topic_data)
                if qna:
                    self.storage.save_qna(qna)

                # 保存打卡任务
                checkin_task = self.processor.parse_checkin_from_topic(topic_data)
                if checkin_task:
                    self.storage.save_checkin_task(checkin_task)
                
                # 标记为已处理
                self.state_manager.mark_topic_processed(topic_id_str)
                success_count += 1
                
                # 定期保存进度
                if success_count % 10 == 0:
                    self.state_manager.save_progress()
                    
            except Exception as e:
                logger.error(f"处理帖子 {topic_id} 时发生错误: {e}")
                continue
        
        # 最终保存进度
        self.state_manager.save_progress()
        logger.info(f"帖子处理完成: {success_count} 条帖子")
        return success_count
    
    def upload_data(self, file_path: str, skip_users: bool = False, skip_group: bool = False, 
                   skip_images: bool = False, use_new_user_workflow: bool = True) -> Dict[str, int]:
        """
        执行完整的数据上传流程

        Args:
            file_path: 数据文件路径
            skip_users: 是否跳过用户处理
            skip_group: 是否跳过星球处理
            skip_images: 是否跳过图片处理和上传
            use_new_user_workflow: 是否使用新的用户处理工作流（包含 mid 获取）

        Returns:
            Dict[str, int]: 处理结果统计
        """
        logger.info("=== 开始数据上传流程 ===")

        try:
            # 加载进度
            self.state_manager.load_progress()

            # 阶段 1: 预扫描数据
            all_users, topics_data, group_info = self.preload_data_from_file(file_path)

            results = {
                'users_processed': 0,
                'group_processed': 0,
                'topics_processed': 0
            }

            # 阶段 2: 处理用户
            if not skip_users:
                if use_new_user_workflow:
                    results['users_processed'] = self.process_users_new(topics_data)
                else:
                    results['users_processed'] = self.process_users(all_users)
            else:
                logger.info("跳过用户处理")

            # 阶段 3: 处理星球
            if not skip_group:
                results['group_processed'] = 1 if self.process_group(group_info) else 0
            else:
                logger.info("跳过星球处理")

            # 阶段 4: 处理帖子
            results['topics_processed'] = self.process_topics(topics_data, skip_images)

            # 阶段 5: 批量处理待处理的用户
            logger.info("--- 阶段 5: 批量处理待处理的用户 ---")
            pending_count = self.user_cache_service.get_pending_users_count()
            if pending_count > 0:
                logger.info(f"发现 {pending_count} 个待处理用户，开始批量处理...")
                batch_result = self.user_cache_service.process_pending_users()
                results['pending_users_processed'] = batch_result
                logger.info(f"批量用户处理完成: {batch_result}")
            else:
                logger.info("没有待处理的用户")
                results['pending_users_processed'] = {'processed': 0, 'failed': 0, 'skipped': 0, 'total': 0}

            logger.info("=== 数据上传流程完成 ===")
            logger.info(f"处理结果: {results}")

            return results

        except Exception as e:
            logger.error(f"上传流程发生异常: {e}")
            raise
    
    def reset_progress(self):
        """重置处理进度"""
        self.state_manager.reset_progress()
        logger.info("处理进度已重置")
    
    def get_progress_info(self) -> Dict[str, int]:
        """获取当前进度信息"""
        user_count, topic_count = self.state_manager.get_processed_counts()
        return {
            'processed_users': user_count,
            'processed_topics': topic_count
        }
