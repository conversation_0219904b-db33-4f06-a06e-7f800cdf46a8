# 打卡任务集成功能（两表结构）

## 功能概述

本次更新为 ZSXQ 数据同步系统添加了打卡任务（checkin tasks）的支持，采用规范化的两表结构设计，能够自动识别和处理知识星球中的打卡活动数据。

## 数据处理逻辑

### 类型识别
- **检查条件**: JSON 数据中是否包含 `checkin` 字段
- **如果包含**: 将帖子类型设置为 `'checkin'`，并额外解析打卡活动信息
- **如果不包含**: 按原有逻辑处理（如 `'talk'`、`'q&a'` 等）

### 数据存储（两表结构）
1. **topics 表**: 存储帖子基础信息
   - `type = 'checkin'` 标识为打卡任务
   - `text` 字段存储打卡内容（如："day1\n好友+7\n朋友圈+2"）

2. **checkin_campaigns 表**: 存储打卡活动核心信息（避免重复）
   - `campaign_id`: 打卡活动唯一ID
   - `title`: 活动标题（如："21 天私域筑基行动营"）
   - `created_at`: 活动发现时间

3. **topic_checkins 表**: 记录帖子与活动的关联关系
   - `topic_id`: 关联的帖子ID
   - `campaign_id`: 关联的活动ID
   - 复合主键确保唯一性

## 数据库部署

### 1. 创建两表结构

在 Supabase SQL 编辑器中执行以下语句：

```sql
-- 1. 创建打卡活动表
CREATE TABLE IF NOT EXISTS checkin_campaigns (
    campaign_id BIGINT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建帖子打卡关联表
CREATE TABLE IF NOT EXISTS topic_checkins (
    topic_id BIGINT NOT NULL,
    campaign_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (topic_id, campaign_id),
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE,
    FOREIGN KEY (campaign_id) REFERENCES checkin_campaigns(campaign_id) ON DELETE CASCADE
);

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS idx_topic_checkins_campaign_id ON topic_checkins(campaign_id);
CREATE INDEX IF NOT EXISTS idx_topic_checkins_topic_id ON topic_checkins(topic_id);
CREATE INDEX IF NOT EXISTS idx_checkin_campaigns_title ON checkin_campaigns(title);

-- 4. 添加注释
COMMENT ON TABLE checkin_campaigns IS '存储打卡活动的核心信息';
COMMENT ON TABLE topic_checkins IS '记录帖子与打卡活动的关联关系';
```

### 2. 验证表创建

```sql
-- 检查表结构
\d checkin_campaigns
\d topic_checkins

-- 测试查询
SELECT * FROM checkin_campaigns LIMIT 1;
SELECT * FROM topic_checkins LIMIT 1;
```

## 代码更改

### 已修改的文件

1. **supabase.sql**: 更新为两表结构（`checkin_campaigns` + `topic_checkins`）
2. **models/data_models.py**:
   - 添加了 `CheckinCampaign` 数据模型
   - 添加了 `TopicCheckin` 数据模型
3. **models/__init__.py**: 导出新的数据模型
4. **services/data_processor.py**:
   - 添加打卡任务类型识别逻辑
   - 更新 `parse_checkin_from_topic()` 方法返回两个对象
5. **services/storage_service.py**:
   - 添加 `save_checkin_campaign()` 方法（避免重复存储）
   - 添加 `save_topic_checkin()` 方法
   - 添加 `save_checkin_data()` 统一保存方法
6. **workflows/upload_workflow.py**: 更新处理流程使用新的两表结构

### 新增功能

- 自动识别包含 `checkin` 字段的帖子
- 解析打卡活动信息（ID、标题）
- 规范化存储：活动信息存 `checkin_campaigns`，关联关系存 `topic_checkins`
- 避免活动信息重复存储
- 支持一个活动多个帖子的场景
- 保持与现有数据处理流程的兼容性

## 使用方式

部署完成后，系统会自动处理打卡任务数据，无需额外配置。现有的同步命令将自动支持新功能：

```bash
# 正常同步，会自动处理打卡任务
python main.py sync -r hour

# 数据清洗也会正确处理打卡任务
python main.py sync -r day
```

## 查询示例

```sql
-- 查询所有打卡任务
SELECT t.topic_id, t.title, t.text, c.title as campaign_title, c.campaign_id
FROM topics t
JOIN topic_checkins tc ON t.topic_id = tc.topic_id
JOIN checkin_campaigns c ON tc.campaign_id = c.campaign_id
WHERE t.type = 'checkin'
ORDER BY t.create_time DESC;

-- 查询特定活动的所有打卡记录
SELECT t.*, c.title as campaign_title
FROM topics t
JOIN topic_checkins tc ON t.topic_id = tc.topic_id
JOIN checkin_campaigns c ON tc.campaign_id = c.campaign_id
WHERE c.campaign_id = 8424545512
ORDER BY t.create_time DESC;

-- 统计各活动的参与人数
SELECT c.title, c.campaign_id, COUNT(DISTINCT t.owner_id) as participant_count
FROM checkin_campaigns c
JOIN topic_checkins tc ON c.campaign_id = tc.campaign_id
JOIN topics t ON tc.topic_id = t.topic_id
GROUP BY c.campaign_id, c.title
ORDER BY participant_count DESC;

-- 查询活动的参与统计
SELECT c.title, c.campaign_id,
       COUNT(*) as total_posts,
       COUNT(DISTINCT t.owner_id) as unique_participants
FROM checkin_campaigns c
JOIN topic_checkins tc ON c.campaign_id = tc.campaign_id
JOIN topics t ON tc.topic_id = t.topic_id
GROUP BY c.campaign_id, c.title
ORDER BY total_posts DESC;
```

## 测试验证

系统已通过以下测试：
- ✅ 数据解析逻辑测试
- ✅ 类型识别准确性测试  
- ✅ 数据模型完整性测试
- ⏳ 端到端数据库存储测试（需要先创建表）

## 两表结构优势

1. **数据规范化**: 避免活动信息重复存储
2. **扩展性强**: 支持一个活动多个帖子的场景
3. **查询灵活**: 便于统计活动参与情况和分析
4. **维护简单**: 活动信息集中管理，便于更新

## 注意事项

1. **向后兼容**: 现有数据和功能不受影响
2. **数据完整性**: 通过外键约束和复合主键确保数据一致性
3. **性能优化**: 添加了必要的数据库索引
4. **错误处理**: 包含完整的异常处理和日志记录
5. **重复处理**: 自动检测并避免重复存储相同的活动信息

部署完成后，系统将能够完整处理知识星球的打卡任务数据，并提供更好的数据结构支持！
