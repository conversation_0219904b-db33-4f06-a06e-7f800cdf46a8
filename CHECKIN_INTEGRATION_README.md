# 打卡任务集成功能

## 功能概述

本次更新为 ZSXQ 数据同步系统添加了打卡任务（checkin tasks）的支持，能够自动识别和处理知识星球中的打卡活动数据。

## 数据处理逻辑

### 类型识别
- **检查条件**: JSON 数据中是否包含 `checkin` 字段
- **如果包含**: 将帖子类型设置为 `'checkin'`，并额外解析打卡任务信息
- **如果不包含**: 按原有逻辑处理（如 `'talk'`、`'q&a'` 等）

### 数据存储
1. **topics 表**: 存储帖子基础信息
   - `type = 'checkin'` 标识为打卡任务
   - `text` 字段存储打卡内容（如："day1\n好友+7\n朋友圈+2"）
   
2. **checkin_tasks 表**: 存储打卡活动信息
   - `topic_id`: 关联的帖子ID
   - `checkin_id`: 打卡活动ID
   - `title`: 活动标题（如："21 天私域筑基行动营"）
   - `min_words_count`: 最少字数要求

## 数据库部署

### 1. 创建 checkin_tasks 表

在 Supabase SQL 编辑器中执行以下语句：

```sql
-- 创建打卡任务表
CREATE TABLE IF NOT EXISTS checkin_tasks (
    topic_id BIGINT PRIMARY KEY,
    checkin_id BIGINT NOT NULL,
    title VARCHAR(255),
    min_words_count INT DEFAULT 0,
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_checkin_tasks_checkin_id ON checkin_tasks(checkin_id);

-- 添加注释
COMMENT ON TABLE checkin_tasks IS '存储打卡任务的专属信息，与topics表一对一关联。';
COMMENT ON COLUMN checkin_tasks.topic_id IS '关联的帖子ID，对应topics.topic_id。';
COMMENT ON COLUMN checkin_tasks.checkin_id IS '打卡活动的唯一ID，来自API的checkin.checkin_id字段。';
COMMENT ON COLUMN checkin_tasks.title IS '打卡活动的标题，来自API的checkin.title字段。';
```

### 2. 验证表创建

```sql
-- 检查表结构
\d checkin_tasks

-- 测试查询
SELECT * FROM checkin_tasks LIMIT 1;
```

## 代码更改

### 已修改的文件

1. **supabase.sql**: 添加了 `checkin_tasks` 表定义和索引
2. **models/data_models.py**: 添加了 `CheckinTask` 数据模型
3. **models/__init__.py**: 导出新的数据模型
4. **services/data_processor.py**: 
   - 添加打卡任务类型识别逻辑
   - 添加 `parse_checkin_from_topic()` 方法
5. **services/storage_service.py**: 添加 `save_checkin_task()` 方法
6. **workflows/upload_workflow.py**: 集成打卡任务处理流程

### 新增功能

- 自动识别包含 `checkin` 字段的帖子
- 解析打卡活动信息（ID、标题、字数要求）
- 双表存储：基础信息存 `topics`，活动信息存 `checkin_tasks`
- 保持与现有数据处理流程的兼容性

## 使用方式

部署完成后，系统会自动处理打卡任务数据，无需额外配置。现有的同步命令将自动支持新功能：

```bash
# 正常同步，会自动处理打卡任务
python main.py sync -r hour

# 数据清洗也会正确处理打卡任务
python main.py sync -r day
```

## 查询示例

```sql
-- 查询所有打卡任务
SELECT t.topic_id, t.title, t.text, ct.title as activity_title, ct.checkin_id
FROM topics t
JOIN checkin_tasks ct ON t.topic_id = ct.topic_id
WHERE t.type = 'checkin'
ORDER BY t.create_time DESC;

-- 查询特定活动的所有打卡记录
SELECT t.*, ct.title as activity_title
FROM topics t
JOIN checkin_tasks ct ON t.topic_id = ct.topic_id
WHERE ct.checkin_id = 8424545512
ORDER BY t.create_time DESC;

-- 统计各活动的参与人数
SELECT ct.title, ct.checkin_id, COUNT(*) as participant_count
FROM checkin_tasks ct
JOIN topics t ON ct.topic_id = t.topic_id
GROUP BY ct.checkin_id, ct.title
ORDER BY participant_count DESC;
```

## 测试验证

系统已通过以下测试：
- ✅ 数据解析逻辑测试
- ✅ 类型识别准确性测试  
- ✅ 数据模型完整性测试
- ⏳ 端到端数据库存储测试（需要先创建表）

## 注意事项

1. **向后兼容**: 现有数据和功能不受影响
2. **数据完整性**: 打卡任务数据通过外键约束确保一致性
3. **性能优化**: 添加了必要的数据库索引
4. **错误处理**: 包含完整的异常处理和日志记录

部署完成后，系统将能够完整处理知识星球的打卡任务数据！
