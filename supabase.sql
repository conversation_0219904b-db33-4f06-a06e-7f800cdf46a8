-- #####################################################################
-- #                                                                   #
-- #          知识星球 API 数据存储数据库脚本 (Supabase 专用)           #
-- #                                                                   #
-- #####################################################################

-- =====================================================================
-- 模块一：用户与服务核心 (The User & Service Core)
-- =====================================================================

-- =====================================================================
-- 1. 用户信息表 (users)
-- 存储所有用户信息，头像字段适配 Supabase Storage。
-- =====================================================================
CREATE TABLE public.users
(
    -- 内部关联核心
    id                      BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,

    -- 业务身份核心 (来自知识星球)
    zsxq_group_member_id    BIGINT UNIQUE NOT NULL, -- mid
    user_id                 BIGINT UNIQUE,          -- uid, 允许为空

    -- 基础资料
    name                    VARCHAR(255),
    alias                   VARCHAR(255),
    avatar_path             VARCHAR(512),
    description             TEXT,
    join_time               TIMESTAMP,
    expiration_time         TIMESTAMP,

    -- 个性化服务信息 (由我们自己运营采集)
    phone_number            VARCHAR(50) UNIQUE,
    wechat_id               VARCHAR(100) UNIQUE,

    -- 未来扩展字段 (使用JSONB)
    profile_data            JSONB
);

COMMENT ON TABLE public.users IS '【用户中心-最终版】系统的绝对核心，记录了用户的星球身份、社群资格以及我们运营所需的核心服务信息。';
COMMENT ON COLUMN public.users.id IS '系统内部ID：由系统自动生成的唯一数字，是所有其他表关联到用户的“内部身份证号”，与业务无关。';
COMMENT ON COLUMN public.users.zsxq_group_member_id IS '社群成员ID (mid): 用户在我们这个社群内的专属成员ID，这是我们识别成员身份的主要业务ID。';
COMMENT ON COLUMN public.users.user_id IS '知识星球用户ID (uid): 【重要】用户在整个知识星球平台的唯一ID。此字段允许为空，因为只有当用户发言后我们才能获取到它。';
COMMENT ON COLUMN public.users.name IS '昵称：用户在知识星球的昵称。';
COMMENT ON COLUMN public.users.alias IS '别名：用户在知识星球的别名。';
COMMENT ON COLUMN public.users.avatar_path IS '头像地址：用户的头像图片存储位置。';
COMMENT ON COLUMN public.users.description IS '个人简介：用户的个人简介。';
COMMENT ON COLUMN public.users.join_time IS '加入社群时间：用户加入我们这个社群的具体时间。';
COMMENT ON COLUMN public.users.expiration_time IS '社群过期时间：用户的付费会员资格到期时间。';
COMMENT ON COLUMN public.users.phone_number IS '手机号：用于联系和发放福利。';
COMMENT ON COLUMN public.users.wechat_id IS '微信号：用于社群服务和联系。';
COMMENT ON COLUMN public.users.profile_data IS '个性化资料库：这是一个“万能抽屉”，未来任何新增的零散信息（如：T恤尺码、饮食偏好等）都可以存放在这里。';


-- ---------------------------------------------------------------------
-- 1.2 收货地址表 (shipping_addresses)
-- ---------------------------------------------------------------------
CREATE TABLE public.shipping_addresses
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id             BIGINT NOT NULL, -- 【变更】现在关联到 users.id
    is_default          BOOLEAN DEFAULT FALSE,
    recipient_name      VARCHAR(255) NOT NULL,
    recipient_phone     VARCHAR(50) NOT NULL,
    country             VARCHAR(100),
    province            VARCHAR(100),
    city                VARCHAR(100),
    district            VARCHAR(100),
    address_line1       TEXT NOT NULL,
    postal_code         VARCHAR(20),
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE
);

COMMENT ON TABLE public.shipping_addresses IS '【用户地址簿】专门存放用户的收货地址。';
COMMENT ON COLUMN public.shipping_addresses.user_id IS '系统内部ID：这条地址属于哪个用户，关联到users.id。';

-- =====================================================================
-- 模块二：核心内容与社群互动 (The Content & Community Core)
-- =====================================================================

-- ---------------------------------------------------------------------
-- 2.1 星球信息表 (groups)
-- ---------------------------------------------------------------------
CREATE TABLE groups (
    group_id BIGINT PRIMARY KEY,            -- 星球唯一ID，来自API
    name VARCHAR(255),                      -- 星球名称
    type VARCHAR(50),                       -- 星球类型，如 'pay' (付费)
    background_path VARCHAR(512)            -- 星球背景图在Supabase Storage中的存储路径 (例如: public/group-backgrounds/48415154841458.jpg)
);

COMMENT ON TABLE public.groups IS '存储知识星球本身的信息。';
COMMENT ON COLUMN public.groups.background_path IS '星球背景图在Supabase Storage中的存储路径。';


-- ---------------------------------------------------------------------
-- 2.2 主题/帖子表 (topics)
-- ---------------------------------------------------------------------
CREATE TABLE topics (
    topic_id BIGINT PRIMARY KEY,            -- 帖子唯一ID，来自API
    group_id BIGINT,                        -- 所属星球的ID (外键)
    type VARCHAR(50),                       -- 帖子类型 (如: talk, solution, q&a, task)
    owner_id BIGINT,                        -- 帖子作者的用户ID (外键)
    text TEXT,                              -- 帖子正文内容
    likes_count INT DEFAULT 0,              -- 点赞数
    comments_count INT DEFAULT 0,           -- 评论数
    reading_count INT,                      -- 阅读数 (API中字段)
    readers_count INT,                      -- 阅读人数
    digested BOOLEAN DEFAULT FALSE,         -- 是否为精华帖
    sticky BOOLEAN DEFAULT FALSE,           -- 是否置顶
    create_time TIMESTAMP,                  -- 帖子创建时间
    modify_time TIMESTAMP,                  -- 帖子最后修改时间
    title VARCHAR(255),                     -- 帖子标题 (通常是正文的缩略)
    FOREIGN KEY (group_id) REFERENCES groups(group_id), -- 关联到星球表
    FOREIGN KEY (owner_id) REFERENCES users(id)    -- 关联到用户表
);

COMMENT ON TABLE public.topics IS '核心的帖子信息表，存储所有类型帖子的通用数据。';
COMMENT ON COLUMN public.topics.owner_id IS '作者的系统内部ID：这篇帖子的作者，关联到users.id。';


-- ---------------------------------------------------------------------
-- 2.3 帖子图片表 (topic_images)
-- ---------------------------------------------------------------------
CREATE TABLE topic_images (
    image_id BIGINT PRIMARY KEY,           -- 图片唯一ID，来自API
    topic_id BIGINT,                       -- 所属帖子的ID (外键)
    path VARCHAR(512),                     -- 图片在Supabase Storage中的存储路径 (例如: public/topic-images/5125442111188484/5122158515551254.jpg)
    type VARCHAR(20),                      -- 图片格式 (如: jpg, png)
    width INT,                             -- 图片宽度
    height INT,                            -- 图片高度
    size_bytes INT,                        -- 图片文件大小 (单位: 字节)
    blur_data_url   TEXT,                  -- 图片模糊预览的DataURL
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE -- 关联到帖子表, 删除帖子时自动删除关联图片记录
);

COMMENT ON TABLE public.topic_images IS '存储帖子的图片信息，一张帖子可以对应多条记录。';
COMMENT ON COLUMN public.topic_images.path IS '图片在Supabase Storage中的存储路径。';


-- ---------------------------------------------------------------------
-- 2.4 帖子点赞表 (topic_likes)
-- ---------------------------------------------------------------------
CREATE TABLE topic_likes (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY, -- 使用Supabase推荐的自增主键
    topic_id BIGINT,                      -- 被点赞帖子的ID (外键)
    user_id BIGINT,                       -- 点赞用户的ID (外键)
    create_time TIMESTAMP,                -- 点赞发生的时间
    emoji_key VARCHAR(50),                -- 点赞使用的表情 (例如: '[强]')
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE, -- 关联到帖子表
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,     -- 如果用户被删除，点赞记录的user_id设为NULL
    UNIQUE (topic_id, user_id, emoji_key) -- 确保一个用户对一个帖子的一种表情只能点赞一次
);

COMMENT ON TABLE public.topic_likes IS '【点赞记录】记录用户对帖子的点赞行为。';
COMMENT ON COLUMN public.topic_likes.user_id IS '点赞人的系统内部ID：关联到users.id。';


-- ---------------------------------------------------------------------
-- 2.5 帖子评论表 (topic_comments)
-- ---------------------------------------------------------------------
CREATE TABLE topic_comments (
    comment_id BIGINT PRIMARY KEY,         -- 评论唯一ID，来自API
    topic_id BIGINT,                       -- 所属帖子的ID (外键)
    owner_id BIGINT,                       -- 发表评论的用户的ID (外键)
    text TEXT,                             -- 评论内容
    likes_count INT DEFAULT 0,             -- 评论获得的点赞数
    rewards_count INT DEFAULT 0,           -- 评论获得的打赏数
    sticky BOOLEAN DEFAULT FALSE,          -- 评论是否置顶
    create_time TIMESTAMP,                 -- 评论创建时间
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE, -- 关联到帖子表
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL       -- 如果用户被删除，评论的owner_id设为NULL
);

COMMENT ON TABLE public.topic_comments IS '【评论记录】存储帖子的评论。';
COMMENT ON COLUMN public.topic_comments.owner_id IS '评论人的系统内部ID：关联到users.id。';

-- ---------------------------------------------------------------------
-- 2.6 问答表 (q_and_a)
-- 存储 'q&a' 类型帖子的额外信息。
-- =====================================================================
CREATE TABLE q_and_a (
    topic_id BIGINT PRIMARY KEY,           -- 帖子ID (主键，同时也是外键)
    question_text TEXT,                    -- 提问的内容
    questionee_id BIGINT,                  -- 被提问者的用户ID (外键)
    answer_text TEXT,                      -- 回答的内容
    answered BOOLEAN,                      -- 是否已回答
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE, -- 关联到帖子表
    FOREIGN KEY (questionee_id) REFERENCES users(id) ON DELETE SET NULL  -- 如果被提问用户被删除，questionee_id设为NULL
);

COMMENT ON TABLE public.q_and_a IS '存储问答(q&a)类型帖子的专属信息，如提问对象和回答等。';


-- ---------------------------------------------------------------------
-- 2.6 标签表 (tags) 和 关联表 (topic_tags)
-- ---------------------------------------------------------------------
CREATE TABLE tags (
    tag_id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY, -- 标签的唯一ID
    name VARCHAR(255) UNIQUE NOT NULL,                       -- 标签名称，必须唯一且不为空
);

COMMENT ON TABLE public.tags IS '存储所有文章的标签，确保标签的唯一性和易于管理。';
COMMENT ON COLUMN public.tags.name IS '标签的名称，例如 "数据库设计", "Supabase" 等。';

CREATE TABLE topic_tags (
    topic_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    PRIMARY KEY (topic_id, tag_id), -- 复合主键，确保一个帖子和一个标签的组合是唯一的
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id) ON DELETE CASCADE, -- 如果帖子被删除，这条关联记录也应删除
    FOREIGN KEY (tag_id) REFERENCES tags(tag_id) ON DELETE CASCADE      -- 如果标签被删除（例如管理员合并标签），这条关联也应删除
);

COMMENT ON TABLE public.topic_tags IS '帖子和标签之间的多对多关系连接表。';

-- =====================================================================
-- 2.7 打卡任务表 (checkin_tasks)
-- 存储 'task' 或 'checkin' 类型帖子的额外信息。
-- =====================================================================
CREATE TABLE public.checkin_tasks (
    topic_id BIGINT PRIMARY KEY,           -- 帖子ID (主键，同时也是外键)
    checkin_id BIGINT NOT NULL,            -- 打卡活动本身的ID，用于关联同一个活动的所有打卡
    title VARCHAR(255),                    -- 打卡活动的标题 (例如：“21天私域筑基行动营”)

    -- 建立到主帖子表的外键关联
    FOREIGN KEY (topic_id) REFERENCES public.topics(topic_id) ON DELETE CASCADE
);

COMMENT ON TABLE public.checkin_tasks IS '【打卡任务】存储“任务打卡”类型帖子的专属信息，例如打卡活动的主题和ID。';
COMMENT ON COLUMN public.checkin_tasks.topic_id IS '关联的帖子ID (主键 & 外键)，指向 topics 表。';
COMMENT ON COLUMN public.checkin_tasks.checkin_id IS '打卡活动本身的ID，来自API，用于将同一活动的所有打卡帖子关联起来。';
COMMENT ON COLUMN public.checkin_tasks.title IS '打卡活动的标题，例如：“21天私域筑基行动营”。';

-- =====================================================================
-- 模块三：荣誉与激励系统 (The Honor & Incentive System)
-- =====================================================================

-- ---------------------------------------------------------------------
-- 3.1 俱乐部定义表 (clubs)
-- ---------------------------------------------------------------------
CREATE TABLE public.clubs
(
    id              SERIAL PRIMARY KEY,
    name            VARCHAR(100) UNIQUE NOT NULL,
    tier_level      INT UNIQUE NOT NULL,
    description     TEXT,
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);
COMMENT ON TABLE public.clubs IS '【俱乐部模板】定义所有俱乐部的名称、等级和描述。';
COMMENT ON COLUMN public.clubs.id IS '俱乐部编号';
COMMENT ON COLUMN public.clubs.name IS '俱乐部名称：例如：“千里会”。';
COMMENT ON COLUMN public.clubs.tier_level IS '俱乐部等级：数字越大等级越高。';
COMMENT ON COLUMN public.clubs.description IS '俱乐部描述';

-- ---------------------------------------------------------------------
-- 3.2 俱乐部成员表 (club_members)
-- ---------------------------------------------------------------------
CREATE TABLE public.club_members
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id             BIGINT NOT NULL, -- 现在关联到 users.id
    club_id             INT NOT NULL,
    membership_number   VARCHAR(50) UNIQUE,
    status              VARCHAR(50) DEFAULT 'active' NOT NULL,
    join_date           DATE NOT NULL,
    notes               TEXT,
    FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE,
    FOREIGN KEY (club_id) REFERENCES public.clubs(id) ON DELETE CASCADE,
    UNIQUE (user_id, club_id)
);
COMMENT ON TABLE public.club_members IS '【会员名册】记录了“谁”加入了“哪个俱乐部”。';
COMMENT ON COLUMN public.club_members.user_id IS '用户的系统内部ID：关联到users.id。';
COMMENT ON COLUMN public.club_members.club_id IS '俱乐部ID';
COMMENT ON COLUMN public.club_members.membership_number IS '会员编号：如“QLH-007”。';
COMMENT ON COLUMN public.club_members.status IS '成员状态：如“活跃(active)”。';
COMMENT ON COLUMN public.club_members.join_date IS '加入日期';

-- ---------------------------------------------------------------------
-- 3.3 元宝余额表 (yuanbao_balances)
-- ---------------------------------------------------------------------
CREATE TABLE public.yuanbao_balances
(
    user_id          BIGINT PRIMARY KEY, -- 【注意】这里的主键是 users.id
    gold_balance     INT DEFAULT 0,
    silver_balance   INT DEFAULT 0,
    copper_balance   INT DEFAULT 0,
    last_updated_at  TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    FOREIGN KEY (user_id) REFERENCES public.users (id) ON DELETE CASCADE
);
COMMENT ON TABLE public.yuanbao_balances IS '【元宝钱包】记录每个成员当前的元宝确切数量。';
COMMENT ON COLUMN public.yuanbao_balances.user_id IS '用户的系统内部ID：关联到users.id。';

-- ---------------------------------------------------------------------
-- 3.4 元宝流水表 (yuanbao_transactions)
-- ---------------------------------------------------------------------
CREATE TABLE public.yuanbao_transactions
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id          BIGINT NOT NULL, -- 现在关联到 users.id
    gold_change      INT NOT NULL DEFAULT 0,
    silver_change    INT NOT NULL DEFAULT 0,
    copper_change    INT NOT NULL DEFAULT 0,
    description      TEXT,
    created_at       TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by       BIGINT,          -- 现在关联到 users.id
    FOREIGN KEY (user_id) REFERENCES public.users (id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES public.users (id) ON DELETE SET NULL
);
COMMENT ON TABLE public.yuanbao_transactions IS '【元宝账本】详细记录每一笔元宝的“收入”和“支出”。';
COMMENT ON COLUMN public.yuanbao_transactions.user_id IS '用户的系统内部ID：关联到users.id。';
COMMENT ON COLUMN public.yuanbao_transactions.created_by IS '操作人的系统内部ID：关联到users.id。';



-- topics 表
CREATE INDEX idx_topics_owner_id ON topics(owner_id);
CREATE INDEX idx_topics_group_id ON topics(group_id);
CREATE INDEX idx_topics_create_time ON topics(create_time); -- 如果经常按时间排序或筛选
-- 1. 为所有帖子的时间排序创建基础索引 (通用，最重要)
CREATE INDEX IF NOT EXISTS idx_topics_create_time_desc ON topics(create_time DESC);
-- 2. 为“精华帖”列表创建高效的部分索引
CREATE INDEX IF NOT EXISTS idx_topics_digested_sorted ON topics(create_time DESC) WHERE digested = TRUE;
-- 为了提高查询效率，添加一个索引，只查询精华帖
CREATE INDEX idx_topics_digested_true ON topics(digested) WHERE digested = TRUE;

-- topic_images 表
-- topic_id 已经是外键，添加索引是好习惯
CREATE INDEX idx_topic_images_topic_id ON topic_images(topic_id);

-- topic_likes 表
-- (topic_id, user_id, emoji_key) 的UNIQUE约束已经创建了索引
-- 但如果经常单独查询某个用户的所有点赞，可以加一个
CREATE INDEX idx_topic_likes_user_id ON topic_likes(user_id);

-- topic_comments 表
CREATE INDEX idx_topic_comments_owner_id ON topic_comments(owner_id);
CREATE INDEX idx_topic_comments_topic_id ON topic_comments(topic_id);

-- tag
-- 3. 为“按标签筛选”功能在关联表上创建复合索引 (解决JOIN性能的关键)
CREATE INDEX IF NOT EXISTS idx_topic_tags_tag_id_topic_id ON topic_tags(tag_id, topic_id);
-- 4. (可选但推荐) 为关联表中的 topic_id 也创建一个独立索引，有助于反向查询（如：查找某篇帖子的所有标签）
CREATE INDEX IF NOT EXISTS idx_topic_tags_topic_id ON topic_tags(topic_id);

-- =====================================================================
-- 脚本结束
-- =====================================================================
